// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.advisortag

import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.advisortag.FundSubAdvisorTagService.{validateAdvisorTag, given}
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.datalakeingestion.model.{RemoveAdvisorTagParams, UpdateOrAddAdvisorTagParams}
import anduin.fundsub.endpoint.advisortag.*
import anduin.fundsub.service.FundSubPermissionService
import anduin.fundsub.utils.FundSubDataLakeUtils
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.model.common.user.UserId
import anduin.model.id.FundSubAdvisorTagIdFactory
import anduin.protobuf.fundsub.advisortag.FundSubAdvisorTagModel
import anduin.service.GeneralServiceException
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

final case class FundSubAdvisorTagService(
  fundSubDataLakeIngestionService: FundSubDataLakeIngestionService,
  userProfileService: UserProfileService,
  fundSubPermissionService: FundSubPermissionService
) {

  def createAdvisorTag(
    params: CreateAdvisorTagParams,
    actor: UserId
  ): Task[CreateAdvisorTagResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor creates new advisor tag for fund ${params.fundSubId}")

      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        fundId = params.fundSubId,
        userId = actor
      )

      _ <- validateAdvisorTag(params.fundSubId, params.name)

      id <- ZIO.succeed(FundSubAdvisorTagIdFactory.unsafeRandomId(params.fundSubId))
      now <- ZIO.succeed(DateCalculator.instantNow)

      tagModel = FundSubAdvisorTagModel(
        id = id,
        name = params.name,
        color = params.color,
        updatedBy = Some(actor),
        updatedAt = Some(now)
      )

      _ <- FDBRecordDatabase.transact(FundSubAdvisorTagOperations.Production)(
        _.create(tagModel)
      )

      _ <- updateOrAddTagToDataLake(
        tagId = id,
        name = params.name
      )

    } yield CreateAdvisorTagResponse()
  }

  def getAdvisorTags(
    params: GetAdvisorTagsParams,
    actor: UserId
  ): Task[GetAdvisorTagsResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor gets advisor tags for fund ${params.fundSubId}")

      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        fundId = params.fundSubId,
        userId = actor
      )

      tags <- FDBRecordDatabase
        .transact(FundSubAdvisorTagOperations.Production) { ops =>
          ops.getByFund(params.fundSubId)
        }
        .map(_.filterNot(_.isRemoved).sortBy(_.name))

    } yield GetAdvisorTagsResponse(tags)
  }

  def updateAdvisorTag(
    params: UpdateAdvisorTagParams,
    actor: UserId
  ): Task[UpdateAdvisorTagResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor updates advisor tag ${params.tagId} for fund ${params.tagId.parent}")

      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        fundId = params.tagId.parent,
        userId = actor
      )

      _ <- ZIOUtils.traverseOption(params.name) { name =>
        validateAdvisorTag(params.tagId.parent, name)
      }

      _ <- FDBRecordDatabase.transact(FundSubAdvisorTagOperations.Production) { ops =>
        ops.update(params.tagId) { tag =>
          tag.copy(
            name = params.name.getOrElse(tag.name),
            color = params.color.getOrElse(tag.color),
            updatedAt = Some(DateCalculator.instantNow),
            updatedBy = Some(actor)
          )
        }
      }

    } yield UpdateAdvisorTagResponse()
  }

  def deleteAdvisorTag(
    params: DeleteAdvisorTagParams,
    actor: UserId
  ): Task[DeleteAdvisorTagResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor deletes advisor tag ${params.tagId} for fund ${params.tagId.parent}")

      _ <- fundSubPermissionService.validateUserHasFundManagerRole(
        fundId = params.tagId.parent,
        userId = actor
      )

      _ <- FDBRecordDatabase.transact(FundSubAdvisorTagOperations.Production) { ops =>
        ops.markAsRemoved(params.tagId)
      }

      _ <- removeTagFromDataLake(params.tagId)

    } yield DeleteAdvisorTagResponse()
  }

  private def updateOrAddTagToDataLake(
    tagId: FundSubAdvisorTagId,
    name: String
  ): Task[Unit] = {
    for {
      _ <- FundSubDataLakeUtils.sendUpdateParams(
        fundId = tagId.parent,
        params = UpdateOrAddAdvisorTagParams(
          id = tagId,
          name = name
        ),
        dataLakeService = fundSubDataLakeIngestionService
      )
    } yield ()
  }

  private def removeTagFromDataLake(tagId: FundSubAdvisorTagId): Task[Unit] = {
    val params = RemoveAdvisorTagParams(id = tagId)
    FundSubDataLakeUtils.sendUpdateParams(
      tagId.parent,
      params,
      fundSubDataLakeIngestionService
    )
  }

}

object FundSubAdvisorTagService {

  val TagNameLengthLimit = 255

  given Conversion[FundSubAdvisorTagModel, FundSubAdvisorTag] = { tag =>
    FundSubAdvisorTag(
      id = tag.id,
      name = tag.name,
      color = tag.color
    )
  }

  given Conversion[List[FundSubAdvisorTagModel], List[FundSubAdvisorTag]] =
    _.map(given_Conversion_FundSubAdvisorTagModel_FundSubAdvisorTag)

  def validateAdvisorTag(
    fundSubId: FundSubId,
    name: String
  ): Task[Unit] = {
    ZIOUtils.validate(name.nonEmpty) {
      GeneralServiceException("Tag name must not be empty")
    } *> ZIOUtils.validate(name.length <= TagNameLengthLimit) {
      GeneralServiceException(s"Tag name must not be longer than $TagNameLengthLimit characters")
    } *> ZIOUtils.validate(
      FDBRecordDatabase.transact(FundSubAdvisorTagOperations.Production) { ops =>
        for {
          tags <- ops.getByFund(fundSubId).map(_.filterNot(_.isRemoved))
          allTagNames = tags.map(_.name)
          tagIsUnique = !allTagNames.exists(_.equalsIgnoreCase(name))
        } yield tagIsUnique
      }
    ) {
      GeneralServiceException(s"Tag name $name already exists")
    }
  }

}
