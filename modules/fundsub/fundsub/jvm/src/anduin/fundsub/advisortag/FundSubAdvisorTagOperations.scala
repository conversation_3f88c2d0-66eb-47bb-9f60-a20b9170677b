// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.advisortag

import com.apple.foundationdb.record.TupleRange

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordReadTask, RecordTask}
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.protobuf.fundsub.advisortag.FundSubAdvisorTagModel
import anduin.fundsub.advisortag.FundSubAdvisorTagStoreProvider.{*, given}

final case class FundSubAdvisorTagOperations(store: Store) {

  def get(id: FundSubAdvisorTagId): RecordReadTask[FundSubAdvisorTagModel] = {
    store.get(id)
  }

  def getOpt(id: FundSubAdvisorTagId): RecordReadTask[Option[FundSubAdvisorTagModel]] = {
    store.getOpt(id)
  }

  def getByFund(fundSubId: FundSubId): RecordReadTask[List[FundSubAdvisorTagModel]] = {
    store
      .scanL(
        mapping = fundSubAdvisorTagIdPrimaryKeyMapping,
        tupleRange = TupleRange.prefixedBy(fundSubId.idString)
      )
      .map(_.map(_._2))
  }

  def create(model: FundSubAdvisorTagModel): RecordTask[Unit] = {
    store.create(model).unit
  }

  def update(
    id: FundSubAdvisorTagId
  )(
    fn: FundSubAdvisorTagModel => FundSubAdvisorTagModel
  ): RecordTask[FundSubAdvisorTagModel] = {
    for {
      model <- store.get(id)
      newModel <- RecordIO.succeed(fn(model))
      _ <- store.update(newModel)
    } yield newModel
  }

  def updateOrCreate(
    id: FundSubAdvisorTagId,
    newModel: FundSubAdvisorTagModel,
    updateFn: FundSubAdvisorTagModel => FundSubAdvisorTagModel
  ): RecordTask[Unit] = {
    for {
      existed <- getOpt(id).map(_.nonEmpty)
      _ <-
        if (existed) {
          update(id)(updateFn).unit
        } else {
          create(newModel)
        }
    } yield ()
  }

  def markAsRemoved(id: FundSubAdvisorTagId): RecordTask[FundSubAdvisorTagModel] = {
    for {
      tagOpt <- getOpt(id)
      result <- tagOpt match {
        case Some(tag) =>
          val updatedTag = tag.withIsRemoved(true)
          store.update(updatedTag).map(_ => updatedTag)
        case None =>
          RecordIO.fail(new RuntimeException(s"Tag with id $id not found"))
      }
    } yield result
  }

}

object FundSubAdvisorTagOperations
    extends FDBOperations.Single[RecordEnum, FundSubAdvisorTagOperations](FundSubAdvisorTagStoreProvider)
