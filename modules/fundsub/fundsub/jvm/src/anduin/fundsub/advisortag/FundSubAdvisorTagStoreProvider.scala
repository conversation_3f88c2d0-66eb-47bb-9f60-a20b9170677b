// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.advisortag

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.Key

import anduin.fdb.record.model.FDBTupleConverter
import anduin.fdb.record.model.common.RadixIdTupleConverter
import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider, FDBStoreProviderCompanion}
import anduin.id.fundsub.FundSubAdvisorTagId
import anduin.protobuf.fundsub.advisortag.{AdvisorTagProto, FundSubAdvisorTagModel}

final case class FundSubAdvisorTagStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.FundSubAdvisorTagModelStore.type](
      FDBRecordEnum.FundSubAdvisorTagModelStore,
      AdvisorTagProto
    ) {

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(FundSubAdvisorTagModel.scalaDescriptor.name)
      .setPrimaryKey(FundSubAdvisorTagStoreProvider.primaryKeyExpression)
  }

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq.empty

}

object FundSubAdvisorTagStoreProvider
    extends FDBStoreProviderCompanion[FDBRecordEnum.FundSubAdvisorTagModelStore.type] {

  val RecordTypeName: String = FundSubAdvisorTagModel.scalaDescriptor.name

  private val primaryKeyExpression = Key.Expressions.field("id")

  private given fundSubAdvisorTagIdTupleConverter: FDBTupleConverter[FundSubAdvisorTagId] = {
    RadixIdTupleConverter.instance
  }

  given fundSubAdvisorTagIdPrimaryKeyMapping: Mapping[FundSubAdvisorTagId, FundSubAdvisorTagModel] =
    mappingInstance

}
