syntax = "proto3";

package anduin.protobuf.fundsub.tag.advisor;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "fundsub/advisortag/advisor_tag.proto";


option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.advisortag"
  single_file: true
  import: "anduin.id.fundsub.FundSubAdvisorTagId"
  import: "anduin.model.common.user.UserId"
};

message FundSubAdvisorTagModel {
  string id = 1 [(scalapb.field).type = "FundSubAdvisorTagId"];
  string name = 2;
  bool isRemoved = 3;
  anduin.protobuf.fundsub.tag.advisor.AdvisorTagColor color = 4;
  string updatedBy = 5 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage updatedAt = 6 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  FundSubAdvisorTagModel _FundSubAdvisorTagModel = 1;
}
