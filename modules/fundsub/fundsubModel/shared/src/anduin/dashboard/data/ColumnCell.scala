// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dashboard.data

import java.time.{Instant, LocalDate, ZonedDateTime}

import io.circe.{<PERSON><PERSON>, <PERSON>son}

import anduin.circe.generic.semiauto.{CirceCodec, deriveCodecWithDefaults}
import anduin.dashboard.data.Documents.{AmlKycReview, FileInfo, OrderDocument}
import anduin.dashboard.data.Units.Currency
import anduin.dashboard.model.AmountColumn.CommitmentAmountType
import anduin.dashboard.model.ClientFormCompareData
import anduin.id.amlkyc.AmlCheckId
import anduin.id.funddata.FundDataInvestorId
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubCloseId, FundSubLpTagId}
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.emailaddress.EmailAddress
import anduin.protobuf.amlcheck.AmlCheckStatus
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpDocRequestStatus, LpStatus}
import anduin.protobuf.fundsub.models.customdata.item.CustomData
import anduin.protobuf.fundsub.{LpOrderType, LpTagColor}
import anduin.protobuf.fundsub.advisortag.AdvisorTagColor
import anduin.utils.DateTimeUtils
// We only ask circe semi-auto to derive the ADT for the root trait
// so there will not be any implicit instance for Decoder[ContactModel]
// we will need to downcast `ContactCell` into `ColumnCell` instead
sealed trait ColumnCell derives CanEqual, CirceCodec.WithDefaultsAndTypeName

// Note that these case classes belong to the public API for the dashboard
// so we should keep them as independent as possible. We should probably not
// expose high level specific details here. Meaning we should use very standard
// data types

case class CloseCell(
  idOpt: Option[FundSubCloseId],
  name: String,
  customCloseId: Option[String],
  targetClosingDate: Option[LocalDate]
) extends ColumnCell derives CirceCodec.WithDefaults

case class CustomLpIdCell(
  customId: String = ""
) extends ColumnCell derives CirceCodec.WithDefaults

case class ContactCell(
  emailAddress: EmailAddress,
  firstName: String,
  lastName: String,
  investmentEntity: String,
  seenByUsers: Seq[User],
  orderType: LpOrderType,
  lastActivityAt: Option[Instant],
  emailBouncedUsers: Seq[User] = Seq.empty,
  joined: Boolean = true,
  completed: Boolean = false,
  collaborators: Seq[CollaboratorInfo] = Seq.empty
) extends ColumnCell derives CirceCodec.WithDefaults {

  lazy val firmNameOrFullName: String = if (investmentEntity.nonEmpty) {
    investmentEntity
  } else {
    s"$firstName $lastName".trim
  }

}

case class CollaboratorInfo(
  emailAddress: String,
  firstName: String,
  lastName: String
) derives CirceCodec.WithDefaults

case class RequiredDocInfo(
  id: String,
  name: String,
  markedAsNa: Boolean,
  submitted: Boolean,
  submittedDocs: List[OrderDocument]
) derives CanEqual,
      CirceCodec.WithDefaults {
  lazy val excludeId: RequiredDocInfo = copy(id = "")

  def sanitize: RequiredDocInfo = this.copy(
    submittedDocs = submittedDocs.map(_.sanitize)
  )

}

case class DocumentRequestCell(
  docs: Seq[RequiredDocInfo],
  reviews: Seq[AmlKycReview],
  providedDocs: Seq[String]
) extends ColumnCell {

  def getDocRequestProgress(
    isSupportingDocReviewEnabled: Boolean
  ): Seq[(DocumentRequestCell.DocumentRequestStatus, Int)] = {
    DocumentRequestCell
      .getDocRequestStatusMap(this, isSupportingDocReviewEnabled = isSupportingDocReviewEnabled)
      .groupBy(_._2)
      .toSeq
      .map { case (status, docs) => (status, docs.size) }
      .filterNot(_._2 == 0) // remove all statusType with 0 docCount
      .sortBy(_._1.index)
      .reverse
  }

}

object DocumentRequestCell {

  given Codec.AsObject[DocumentRequestCell] = deriveCodecWithDefaults

  sealed trait DocumentRequestStatus derives CanEqual {
    def index: Int
    def name: String
    def description: String
  }

  object PendingSubmissionState extends DocumentRequestStatus {
    override def index: Int = 0
    override def name: String = "Pending submission"
    override def description: String = "Show subscriptions with pending requested document submissions"
  }

  object ChangesInProgressState extends DocumentRequestStatus {
    override def index: Int = 1
    override def name: String = "Changes in progress"
    override def description: String = "Show subscriptions awaiting investors to update requested documents"
  }

  object PendingReviewState extends DocumentRequestStatus {
    override def index: Int = 2
    override def name: String = "Pending review"
    override def description: String = "Show subscriptions with requested documents awaiting review"
  }

  object CompletedState extends DocumentRequestStatus {
    override def index: Int = 3
    override def name: String = "Complete"
    override def description: String = "Show subscriptions with all requested documents complete"
  }

  val otherDocumentName = "Other documents"

  def getDocRequestStatus(lpDocRequestStatus: LpDocRequestStatus): DocumentRequestStatus = {
    lpDocRequestStatus match {
      case LpDocRequestStatus.ChangesInProgress => ChangesInProgressState
      case LpDocRequestStatus.PendingReview     => PendingReviewState
      case LpDocRequestStatus.Complete          => CompletedState
      case _                                    => PendingSubmissionState
    }
  }

  def getLpDocRequestStatus(docRequestStatus: DocumentRequestStatus): LpDocRequestStatus = {
    docRequestStatus match {
      case ChangesInProgressState => LpDocRequestStatus.ChangesInProgress
      case PendingReviewState     => LpDocRequestStatus.PendingReview
      case CompletedState         => LpDocRequestStatus.Complete
      case PendingSubmissionState => LpDocRequestStatus.PendingSubmission
    }
  }

  def getDocRequestProgressString(progress: Seq[(DocumentRequestCell.DocumentRequestStatus, Int)]): String = {
    val allDocRequestComplete = {
      progress.nonEmpty && progress.forall(_._1 == DocumentRequestCell.CompletedState)
    }
    if (allDocRequestComplete) {
      DocumentRequestCell.CompletedState.name
    } else {
      progress.map { case (statusType, docCount) =>
        s"$docCount ${statusType.name.toLowerCase}\n"
      }.mkString
    }
  }

  final case class SupportingDocumentRequestInfo(
    docType: String,
    submitted: Boolean,
    markedAsNa: Boolean
  )

  final case class SupportingDocumentReviewInfo(
    docType: String,
    status: ReviewStatus
  )

  def getDocRequestStatusMap(
    cell: DocumentRequestCell,
    isSupportingDocReviewEnabled: Boolean
  ): Map[SupportingDocumentRequestInfo, DocumentRequestCell.DocumentRequestStatus] = {
    getDocRequestStatusMap(
      docRequests = cell.docs.map { doc =>
        DocumentRequestCell.SupportingDocumentRequestInfo(doc.name, doc.submitted, doc.markedAsNa)
      },
      docReviews = cell.reviews.map { review =>
        DocumentRequestCell.SupportingDocumentReviewInfo(review.docType, review.status)
      },
      providedDocs = cell.providedDocs,
      isSupportingDocReviewEnabled = isSupportingDocReviewEnabled
    )
  }

  def getDocRequestStatusMap(
    docRequests: Seq[SupportingDocumentRequestInfo],
    docReviews: Seq[SupportingDocumentReviewInfo],
    providedDocs: Seq[String],
    isSupportingDocReviewEnabled: Boolean
  ): Map[SupportingDocumentRequestInfo, DocumentRequestCell.DocumentRequestStatus] = {
    docRequests
      .filterNot(_.docType.equalsIgnoreCase(DocumentRequestCell.otherDocumentName))
      .map { doc =>
        val isPendingReview = docReviews.exists { review =>
          review.docType.trim.equalsIgnoreCase(doc.docType.trim) && (
            review.status == ReviewStatus.Pending
          )
        }
        val isChangesInProgress = docReviews.exists { review =>
          review.docType.equalsIgnoreCase(doc.docType) && (
            review.status == ReviewStatus.ChangesRequested
          )
        }
        val isComplete = doc.submitted || doc.markedAsNa || providedDocs.exists(_.trim.equalsIgnoreCase(doc.docType.trim))
        if (isPendingReview && isSupportingDocReviewEnabled) {
          (doc, DocumentRequestCell.PendingReviewState)
        } else if (isChangesInProgress && isSupportingDocReviewEnabled) {
          (doc, DocumentRequestCell.ChangesInProgressState)
        } else if (isComplete) {
          (doc, DocumentRequestCell.CompletedState)
        } else {
          (doc, DocumentRequestCell.PendingSubmissionState)
        }
      }
      .toMap
  }

}

case class LastActivityCell(
  time: Option[Instant]
) extends ColumnCell derives CirceCodec.WithDefaults {

  lazy val date: Option[LocalDate] = time.map { dateTime =>
    val localTimeZone = DateTimeUtils.getTimezone(None)
    val zonedDateTime = ZonedDateTime.ofInstant(dateTime, localTimeZone)
    zonedDateTime.toLocalDate
  }

}

case class MoneyCell(
  currency: Currency,
  value: Float
) extends ColumnCell derives CirceCodec.WithDefaults

case class CustomDataCell(
  data: Option[CustomData],
  lastEditedByOpt: Option[User],
  lastEditedAtOpt: Option[Instant]
) extends ColumnCell derives CirceCodec.WithDefaults

case class ReferenceDocumentCell(
  files: List[FileInfo]
) extends ColumnCell derives CirceCodec.WithDefaults

case class SignatureRequestCell(
  requests: List[SignatureRequest]
) extends ColumnCell {
  lazy val requested: Int = requests.count(request => request.status != SignatureRequestStatus.Cancelled)
  lazy val signed: Int = requests.count(request => request.status == SignatureRequestStatus.Completed)

  def getState: SignatureRequestCell.SignatureRequestStatus = {
    if (requested == 0) {
      SignatureRequestCell.EmptyState
    } else if (requested == signed) {
      SignatureRequestCell.CompletedState
    } else {
      SignatureRequestCell.PendingState
    }
  }

}

object SignatureRequestCell {

  given Codec.AsObject[SignatureRequestCell] = deriveCodecWithDefaults

  sealed trait SignatureRequestStatus derives CanEqual {
    def index: Int
    def name: String
  }

  object EmptyState extends SignatureRequestStatus {
    override def index: Int = 0
    override def name: String = ""
  }

  object PendingState extends SignatureRequestStatus {
    override def index: Int = 1
    override def name: String = "Pending signature"
  }

  object CompletedState extends SignatureRequestStatus {
    override def index: Int = 2
    override def name: String = "Complete"
  }

}

case class SubscriptionDocumentCell(
  status: LpStatus,
  formProgress: Float,
  firmNameOrLpName: String,
  orderType: LpOrderType,
  clientFormCompareData: Seq[ClientFormCompareData]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class TagInfo(
  name: String,
  color: LpTagColor = LpTagColor.Gray1
) derives CirceCodec.WithDefaults

final case class TagCell(
  tags: List[(FundSubLpTagId, TagInfo)]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class AdvisorTagInfo(
  name: String,
  color: AdvisorTagColor = AdvisorTagColor.Gray1
) derives CirceCodec.WithDefaults

final case class AdvisorTagCell(
  tags: List[(FundSubAdvisorTagId, AdvisorTagInfo)]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class AmountCell(
  value: Option[Double],
  actualAmountType: CommitmentAmountType
) extends ColumnCell derives CirceCodec.WithDefaults

case class FormFieldCell(
  value: Json
) extends ColumnCell derives CirceCodec.WithDefaults

case class InvitationDateCell(
  value: Option[Instant]
) extends ColumnCell derives CirceCodec.WithDefaults

case class SubmissionDateCell(
  value: Option[Instant]
) extends ColumnCell derives CirceCodec.WithDefaults

case class FormProgressCell(
  value: Double
) extends ColumnCell derives CirceCodec.WithDefaults

case class CommitmentPercentageCell(
  value: Option[Double]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class ClientInfo(
  clientId: FundDataInvestorId,
  clientRestrictedInfo: Option[ClientRestrictedInfo]
) derives CirceCodec.WithDefaults

final case class ClientRestrictedInfo(
  clientName: String,
  investmentEntityName: Option[String],
  clientCustomId: Option[String]
) derives CirceCodec.WithDefaults

case class ClientCell(
  clients: List[ClientInfo]
) extends ColumnCell derives CirceCodec.WithDefaults

case class DataExtractionCell(
  dataExtractionRequestOpt: Option[DataExtractionRequestDashboardSchema.DataExtractionRequestDashboardInfo]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class DashboardAmlCheckSchema(
  id: AmlCheckId,
  status: AmlCheckStatus
) derives CirceCodec.WithDefaults

final case class AmlCheckCell(
  amlChecks: List[DashboardAmlCheckSchema]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class AdvisorGroupBasicInfo(
  id: FundSubRiaGroupId,
  name: String
) derives CirceCodec.WithDefaults

final case class AdvisorGroupCell(
  advisorGroupOpt: Option[AdvisorGroupBasicInfo]
) extends ColumnCell derives CirceCodec.WithDefaults

final case class SideLetterCell(
  status: SideLetterWorkflowStatus
) extends ColumnCell derives CirceCodec.WithDefaults

final case class InvestorGroupBasicInfo(
  id: FundSubInvestorGroupId,
  name: String
) derives CirceCodec.WithDefaults

final case class InvestorGroupCell(
  investorGroupOpt: Option[InvestorGroupBasicInfo]
) extends ColumnCell derives CirceCodec.WithDefaults
