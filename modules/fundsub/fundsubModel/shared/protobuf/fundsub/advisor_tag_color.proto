syntax = "proto3";

package anduin.protobuf.fundsub.tag.advisor;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.advisortag"
  single_file: true
};

enum AdvisorTagColor {
  Gray1 = 0;
  Red = 1;
  Orange = 2;
  YellowOrange = 3;
  Yellow = 4;
  YellowGreen = 5;
  Green = 6;
  BlueGreen = 7;
  Aqua = 8;
  Blue = 9;
  Indigo = 10;
  Purple = 11;
  Magenta = 12;
  HotPink = 13;
  Pink = 14;
  Gray7 = 15;
}
