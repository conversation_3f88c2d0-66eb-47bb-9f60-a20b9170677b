// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.endpoint.advisortag

import java.time.Instant

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.UserId
import anduin.protobuf.fundsub.advisortag.AdvisorTagColor

final case class GetAdvisorTagsParams(
  fundSubId: FundSubId
)

object GetAdvisorTagsParams {
  given Codec.AsObject[GetAdvisorTagsParams] = deriveCodecWithDefaults
}

final case class FundSubAdvisorTag(
  id: FundSubAdvisorTagId,
  name: String,
  color: AdvisorTagColor
) derives CanEqual

object FundSubAdvisorTag {
  given Codec.AsObject[FundSubAdvisorTag] = deriveCodecWithDefaults
}

final case class GetAdvisorTagsResponse(
  tags: Seq[FundSubAdvisorTag]
)

object GetAdvisorTagsResponse {
  given Codec.AsObject[GetAdvisorTagsResponse] = deriveCodecWithDefaults
}

final case class CreateAdvisorTagParams(
  fundSubId: FundSubId,
  name: String,
  color: AdvisorTagColor
)

object CreateAdvisorTagParams {
  given Codec.AsObject[CreateAdvisorTagParams] = deriveCodecWithDefaults
}

final case class CreateAdvisorTagResponse()

object CreateAdvisorTagResponse {
  given Codec.AsObject[CreateAdvisorTagResponse] = deriveCodecWithDefaults
}

final case class UpdateAdvisorTagParams(
  tagId: FundSubAdvisorTagId,
  name: Option[String],
  color: Option[AdvisorTagColor]
)

object UpdateAdvisorTagParams {
  given Codec.AsObject[UpdateAdvisorTagParams] = deriveCodecWithDefaults
}

final case class UpdateAdvisorTagResponse()

object UpdateAdvisorTagResponse {
  given Codec.AsObject[UpdateAdvisorTagResponse] = deriveCodecWithDefaults
}

final case class DeleteAdvisorTagParams(
  tagId: FundSubAdvisorTagId
)

object DeleteAdvisorTagParams {
  given Codec.AsObject[DeleteAdvisorTagParams] = deriveCodecWithDefaults
}

final case class DeleteAdvisorTagResponse()

object DeleteAdvisorTagResponse {
  given Codec.AsObject[DeleteAdvisorTagResponse] = deriveCodecWithDefaults
}
